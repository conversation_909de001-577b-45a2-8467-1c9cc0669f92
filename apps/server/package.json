{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"dotenv": "^17.2.1", "zod": "^4.0.2", "@orpc/server": "^1.5.0", "@orpc/client": "^1.5.0", "fastify": "^5.3.3", "@fastify/cors": "^11.0.1", "drizzle-orm": "^0.44.2", "@neondatabase/serverless": "^1.0.1", "ws": "^8.18.3", "better-auth": "^1.3.4"}, "devDependencies": {"tsdown": "^0.12.9", "typescript": "^5.8.2", "tsx": "^4.19.2", "@types/node": "^22.13.11", "drizzle-kit": "^0.31.2", "@types/ws": "^8.18.1"}}