{"name": "my-better-t-app", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "pnpm -r dev", "build": "pnpm -r build", "check-types": "pnpm -r check-types", "dev:native": "pnpm --filter native dev", "dev:web": "pnpm --filter web dev", "dev:server": "pnpm --filter server dev", "db:push": "pnpm --filter server db:push", "db:studio": "pnpm --filter server db:studio", "db:generate": "pnpm --filter server db:generate", "db:migrate": "pnpm --filter server db:migrate"}, "packageManager": "pnpm@10.11.0"}